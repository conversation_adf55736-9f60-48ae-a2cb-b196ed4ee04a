import { <PERSON>down<PERSON>enderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import React, { useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import HeaderResearch from '../HeaderResearch';
import { EDeskResearch } from '@/features/project-management/types/questionnaire';
import type { OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import { toast } from 'sonner';
import { EDescriptionCopilotkit, EEndpointApiCopilotkit, ETaskNameCopilot } from '@/shared/enums/global';
import { useParams } from 'next/navigation';
import type { IFileResponse } from '@/shared/types/global';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useCurrentStep, useProjectInfo, useProjectName, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { ScoringReportDataType } from '@/features/project-management/types/project';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';
import { markdownToHTMLToDocFile } from '@/shared/components/ui/editor/parser';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import MessageWarning from '../../common/MessageWarning';
import { useLocale, useTranslations } from 'next-intl';
import { useCopilotAction } from '@copilotkit/react-core';
import AIResult from '../../common/AIResult';
import { useChatBoxMessage } from '@/features/project-management/stores/chatbox-store';
import type { TextUploadWrapperType } from './TextUploadWrapper';

type ResearchTypeView = 'research' | 'summarized';

type TextUploadMarkdownType = {
  data: any[];
  templates: IFileResponse[];
  markdown: string;
  id: string;
  stepId: string;
  evaluationFramework: string;
  markdownSummarized: string;
  conversationIdSummarized: string;
  conversationIdResearch: string;
  typeView: ResearchTypeView;
  nameForm: string;
  ref: React.Ref<TextUploadWrapperType>;
  setMarkdown: (markdown: string) => void;
  onBackDashboard: () => void;
  onBackUploadFile: () => void;
  setIsEditResearch: (status: boolean) => void;
  onOpenDetailScore: (data: string) => void;
  setTypeView: (type: ResearchTypeView) => void;
  setMarkdownSummarized: (data: string) => void;
  getIsLoadingData: (status: boolean) => void;
};

const TextUploadMarkdown: React.FC<TextUploadMarkdownType> = ({
  data,
  templates,
  markdown,
  id,
  stepId,
  evaluationFramework,
  typeView,
  nameForm,
  markdownSummarized: summary,
  conversationIdSummarized,
  conversationIdResearch,
  ref,
  setMarkdown,
  onBackDashboard,
  onBackUploadFile,
  setIsEditResearch,
  onOpenDetailScore,
  setTypeView,
  getIsLoadingData,
  setMarkdownSummarized: setMarkdownSum,
}) => {
  const t = useTranslations('workflow');

  const locale = useLocale();

  const toggleOptions = [
    { id: 'research' as ResearchTypeView, label: t('deskResearch.researchData') },
  ];
  const summarizeOption
  = { id: 'summarized' as ResearchTypeView, label: t('deskResearch.summarizedReport') };

  const [optionList, setOptionList] = useState<OptionChangeViewType[]>(toggleOptions);

  const [selectedType, setSelectedType] = useState<ResearchTypeView>(typeView);

  const [isLoading, setIsLoading] = useState<boolean>(() => !markdown);

  const [isLoadingSummarized, setIsLoadingSummarized] = useState<boolean>(true);

  const [_isViewCopyBox, setIsViewCopyBox] = useState<boolean>(false);

  const [modelAIResearchDefault, setModelAIResearchDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAIResearchSelected, setModelAIResearchSelected] = useState<string>(EValueModelAI.GPT);

  const [modelAISumDefault, setModelAISumDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISumSelected, setModelAISumSelected] = useState<string>(EValueModelAI.GPT);

  const [isGen, setIsGen] = useState<boolean>(false);

  const [scoringReportData, setScoringReportData] = useState<ScoringReportDataType | null>(null);

  const params = useParams<{ id: string }>();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { moveToNextStep } = useWorkflowActions();

  const { mutateAsync: updateDataStatus } = useUpdateStatusStep();

  const queryClient = useQueryClient();

  const currentStep = useCurrentStep();

  const projectName = useProjectName();

  const project = useProjectInfo();

  const lastMessage = useChatBoxMessage();

  const abortControllerRef = useRef<AbortController | null>(null);

  const saveDataInStep = React.useCallback(async (
    markdown: string,
    type: EDeskResearch,
    order: number,
    status: EStatusTask,
    model: string,
  ) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          type,
          order,
          infos: [{
            value: markdown,
            status,
            isFinish: EDeskResearch.SUMMARIZED === type,
          }],
          model,
        },
      ],
    };

    setMarkdownSum('');
    await updateQuestionAnswer(payload, stepId);
  }, [id, setMarkdownSum, stepId, updateQuestionAnswer]);

  const getDataFromAI = async (additionalFile: IFileResponse[]) => {
    const payload = {
      project_id: params.id,
      template_research_url: [...getFile(selectedType === 'research' ? templates.filter(t => t.category === 'questionnaire') : templates.filter(t => t.category === 'report'), true)],
      additional_info_url: [...getFile(additionalFile, true)],
      llm: modelAIResearchSelected,
    };

    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.DESK_RESEARCH_ANSWER_QUESTION }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();

      const markdown = res.data.result;
      setMarkdown(markdown);
      saveDataInStep(
        markdown,
        EDeskResearch.RESEARCH,
        1,
        EStatusTask.IN_PROGRESS,
        modelAIResearchSelected,
      );
      setIsLoading(false);
    } catch (error: any) {
      console.error(error);
    }
  };

  const getScoringContent = async (data: string) => {
    const payload = {
      project_id: params.id,
      evaluation_framework: evaluationFramework,
      content_to_score: data,
      llm: modelAISumSelected,
    };
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;
      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.SCORING_CONTENT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const evaluationReport = res.data.result.evaluate_report;
      const evaluationScore = res.data.result.score;

      const data = { report: evaluationReport, score: evaluationScore };
      const payloadScoring = {
        formStepId: id,
        stepInfos: [
          {
            type: EDeskResearch.SCORING,
            order: 3,
            infos: [{ value: data }],
            model: modelAISumSelected,
          },
        ],
      };

      await updateQuestionAnswer(payloadScoring, stepId);

      setScoringReportData(data);
    } catch (error: any) {
      console.log(error);
    }
  };

  const getDataSummarized = async () => {
    const payload = {
      project_id: params.id,
      report_template_url: [...getFile(selectedType === 'research' ? templates.filter(t => t.category === 'questionnaire') : templates.filter(t => t.category === 'report'), true)],
      desk_research_answer: markdown,
      llm: modelAISumSelected,
    };
    if (abortControllerRef.current) {
      return;
    }
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.DESK_RESEARCH_REPORT }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const markdown = res.data.result;
      await getScoringContent(markdown);
      saveDataInStep(
        markdown,
        EDeskResearch.SUMMARIZED,
        2,
        EStatusTask.COMPLETED,
        modelAISumSelected,
      );
      setIsViewCopyBox(false);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (data.length) {
      const dataTextUpload = data.filter(d => d.type !== EDeskResearch.UPLOAD_FILE);
      if (!dataTextUpload.length) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(true);
        const additionalFile = data.find (d => d.type === EDeskResearch.UPLOAD_FILE);
        if (additionalFile) {
          const files = additionalFile.infos[0].files;
          getDataFromAI(files);
        }
      } else {
        const researchData = dataTextUpload.find(d => d.type === EDeskResearch.RESEARCH);
        const summarizedData = dataTextUpload.find(d => d.type === EDeskResearch.SUMMARIZED);
        const scoringData = dataTextUpload.find(d => d.type === EDeskResearch.SCORING);
        if (researchData) {
          const additionalFile = data.find (d => d.type === EDeskResearch.UPLOAD_FILE);

          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setModelAIResearchDefault(additionalFile.model);

          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setModelAIResearchSelected(additionalFile.model);

          const markdownRes = researchData.infos[0].value;
          const status = researchData.infos[0].status;

          if (!markdown) {
            setMarkdown(markdownRes);
          }

          if (selectedType === 'summarized' && !markdownRes) {
            abortControllerRef.current = null;
            getDataSummarized();
          }
          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsViewCopyBox(status === EStatusTask.COMPLETED);
          if (status === EStatusTask.COMPLETED) {
            // setIsViewEditButton(false);

            if (optionList.length === 1) {
            // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
              setOptionList([...toggleOptions, summarizeOption]);
            }
          }
          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsLoading(false);
        } else {
          const additionalFile = data.find (d => d.type === EDeskResearch.UPLOAD_FILE);
          if (additionalFile) {
            const files = additionalFile.infos[0].files;
            getDataFromAI(files);
          }
        }

        if (summarizedData) {
          const markdown = summarizedData.infos[0].value;
          if (!summary) {
            setMarkdownSum(markdown);
          }

          const model = summarizedData.model;

          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setModelAISumDefault(model);

          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setModelAISumSelected(model);

          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setIsLoadingSummarized(false);

          if (optionList.length === 1) {
            // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
            setOptionList([...toggleOptions, summarizeOption]);
          }
        } else {
          if (selectedType === 'summarized' && !summary) {
            getDataSummarized();
          }
        }

        if (scoringData) {
          const data = scoringData.infos[0].value;
          // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setScoringReportData(data);
        }

        const isGenInform = data.some(d => d.isGenerate && d.order === 2);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsGen(isGenInform);
      }
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    setTypeView(selectedType);
  }, [selectedType, setTypeView]);

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  const isChangedModelResearch = useMemo(() => {
    return modelAIResearchDefault !== modelAIResearchSelected;
  }, [modelAIResearchDefault, modelAIResearchSelected]);

  const isChangedModelSum = useMemo(() => {
    return modelAISumDefault !== modelAISumSelected;
  }, [modelAISumDefault, modelAISumSelected]);

  const handleUpdateMarkdownContent = async (
    llm: string,
    task_name: string,
    original_content: string,
    conversationId: string,
    order: number,
    type: number,
  ) => {
    const projectId = project?.id ?? '';

    const data = {
      project_id: projectId,
      conversation_id: conversationId,
      llm,
      task_name,
      instructions: lastMessage,
      original_content,
    };

    const baseUrl = window.location.origin;
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
      method: 'POST',
      body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.EDIT_CONTENT }),
      signal: abortControllerRef.current.signal,
    });

    const res = await response.json();

    const { result } = res.data;

    const { explain } = result;

    const { output } = result;

    if (typeView === 'summarized') {
      await getScoringContent(output);
    }

    await saveDataInStep(
      output,
      type,
      order,
      EStatusTask.COMPLETED,
      llm,
    );

    return explain;
  };

  useCopilotAction({
    name: 'updateResearchDeskResearch',
    description: EDescriptionCopilotkit.RESEARCH_DESK_RESEARCH,
    available: typeView === 'research' ? 'enabled' : 'disabled',
    parameters: [],
    handler: async () => {
      return await handleUpdateMarkdownContent(
        modelAIResearchSelected,
        ETaskNameCopilot.RESEARCH_DESK_RESEARCH,
        markdown,
        conversationIdResearch,
        1,
        EDeskResearch.RESEARCH,
      );
    },
    render: ({ result }) => {
      return <AIResult result={result} />;
    },
  });

  useCopilotAction({
    name: 'updateReportDeskResearch',
    description: EDescriptionCopilotkit.SUMMARIZED_DESK_RESEARCH,
    available: typeView === 'summarized' ? 'enabled' : 'disabled',
    parameters: [],
    handler: async () => {
      return await handleUpdateMarkdownContent(
        modelAISumSelected,
        ETaskNameCopilot.SUMMARIZED_DESK_RESEARCH,
        summary,
        conversationIdSummarized,
        2,
        EDeskResearch.SUMMARIZED,
      );
    },
    render: ({ result }) => {
      return <AIResult result={result} />;
    },
  });

  const handleSelectType = async (option: OptionChangeViewType) => {
    if (option.id === 'summarized') {
      // setIsViewEditButton(false);
      if (!summary) {
        setIsLoadingSummarized(true);
        abortControllerRef.current = null;
        getDataSummarized();
      }
    }

    setSelectedType(option.id as ResearchTypeView);
  };

  const onSubmit = React.useCallback(async () => {
    const id: string[] = data.filter(d => d.order !== 0 && d.order !== 1).map(t => t.id);
    await updateDataStatus({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [],
      select: 'all',
      isGenerate: true,
      stepInfoIdsGenerate: (selectedType === 'research') ? id : [],
    });
    if (selectedType === 'research') {
      // setIsLoading(true);
      await saveDataInStep(
        markdown,
        EDeskResearch.RESEARCH,
        1,
        EStatusTask.COMPLETED,
        modelAIResearchSelected,
      );

      await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });

      toast.success(t('deskResearch.notificationSuccess'));
    } else {
      setIsLoadingSummarized(true);
      setMarkdownSum('');
      await saveDataInStep(
        summary,
        EDeskResearch.SUMMARIZED,
        2,
        EStatusTask.COMPLETED,
        modelAISumSelected,
      );
    }
    // setIsViewEditButton(false);
  }, [
    data,
    stepId,
    selectedType,
    updateDataStatus,
    // setIsLoading,
    saveDataInStep,
    markdown,
    modelAIResearchSelected,
    queryClient,
    t,
    setIsLoadingSummarized,
    setMarkdownSum,
    summary,
    modelAISumSelected,
  ]);

  const onSaveAndNextStep = async () => {
    if (selectedType === 'research') {
      setSelectedType('summarized');
      if (!summary) {
        setIsLoadingSummarized(true);
        abortControllerRef.current = null;
        getDataSummarized();
      }
    } else {
      if (summary) {
        moveToNextStep();
      }
    }
  };

  const handleReGenData = async () => {
    if (selectedType === 'research') {
      setIsLoading(true);
      setMarkdown('');

      const ids: string[] = data.filter(d => d.order === 1).map(t => t.id);
      const idAddGenerate: string[] = data.filter(d => d.order !== 1 && d.order !== 0).map(t => t.id);

      await updateDataStatus({
        id: stepId,
        status: EStatusTask.COMPLETED,
        stepIds: [],
        stepInfoIds: ids,
        select: 'all',
        isGenerate: true,
        stepInfoIdsGenerate: idAddGenerate,
      });

      const additionalFile = data.find (d => d.type === EDeskResearch.UPLOAD_FILE);

      const payload = {
        formStepId: id,
        stepInfos: [
          {
            infos: [{
              ...additionalFile.infos[0],
            }],
            order: 0,
            type: additionalFile.type,
            model: modelAIResearchSelected,
          },
        ],
      };

      await updateQuestionAnswer(payload, stepId);
    } else {
      const ids: string[] = data.filter(d => d.order !== 0 && d.order !== 1).map(t => t.id);

      setIsLoadingSummarized(true);
      setMarkdownSum('');
      setScoringReportData(null);
      abortControllerRef.current = null;
      await updateDataStatus({
        id: stepId,
        status: EStatusTask.COMPLETED,
        stepIds: [],
        stepInfoIds: ids,
        select: 'all',
        isGenerate: true,
        stepInfoIdsGenerate: [],
      });

      await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });
    }
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(selectedType === 'summarized' ? summary : markdown);

    await downloadMDToFile(html, projectName, nameStep[locale]!, nameForm, selectedType);
  };

  useImperativeHandle(ref, () => {
    return {
      onSubmitRef: () => {
        onSubmit();
      },
    };
  }, [onSubmit]);

  useEffect(() => {
    getIsLoadingData(selectedType === 'summarized' ? isLoadingSummarized : isLoading);
  }, [isLoading, isLoadingSummarized, selectedType, getIsLoadingData]);

  const handelChangeModel = (data: string) => {
    if (selectedType === 'research') {
      setModelAIResearchSelected(data);
    } else {
      setModelAISumSelected(data);
    }
  };
  return (

    <>
      <div className="sticky top-[70px] bg-white z-1 pb-2 dark:bg-black dark:text-white">
        <HeaderResearch
          // isViewCopyBox={isViewCopyBox}
          isScoringAI={selectedType === 'summarized' && !!scoringReportData}
          isViewCopyBox={false}
          isHiddenBackButton={!markdown}
          options={optionList}
          selectedType={selectedType}
          isNotAcceptEdit={false}
          textCopy={markdown}
          isLoading={selectedType === 'summarized' ? isLoadingSummarized : isLoading}
          dataScoring={scoringReportData}
          handleSelectedType={handleSelectType}
          onClickApprove={onSubmit}
          onSaveAndNextStep={onSaveAndNextStep}
          onBackDashboard={onBackDashboard}
          onBackUploadFile={onBackUploadFile}
          onEditData={setIsEditResearch}
          onOpenDetailScore={onOpenDetailScore}
          isShowButtonReGen={
            (isChangedModelResearch)
            || (selectedType === 'summarized' && (isGen || isChangedModelSum))
          }
          onReGen={handleReGenData}
          onDownloadFile={handleDownloadFile}
          isShowDownloadIcon={
            selectedType === 'summarized'
              ? (!!summary && !isLoadingSummarized)
              : (!!markdown && !isLoading)
          }
          onChangeModel={handelChangeModel}
          modelAIDefault={selectedType === 'research'
            ? modelAIResearchDefault
            : modelAISumDefault}
          styleButton={(summary || markdown) ? {} : { opacity: '0.5', cursor: 'not-allowed' }}
        />
      </div>

      {/* { isLoading
        ? (
            <ProjectCardSkeleton />
          )
        : ( */}
      <div className="relative dark:bg-black">
        {selectedType === 'research'
          ? (
              isLoading
                ? (
                    <React.Fragment>
                      <ProjectCardSkeleton />
                      <MessageWarning />
                    </React.Fragment>
                  )
                : <MarkdownRenderer content={markdown} />)
          : (isLoadingSummarized
              ? (
                  <React.Fragment>
                    <ProjectCardSkeleton />
                    <MessageWarning />
                  </React.Fragment>
                )
              : <MarkdownRenderer content={summary} />
            )}
      </div>
      {/* )} */}
    </>
  );
};

export default TextUploadMarkdown;
