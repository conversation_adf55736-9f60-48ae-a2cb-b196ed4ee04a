import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import HeaderResearch from '../HeaderResearch';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import type { ConversationDataType, OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import type { IFileResponse } from '@/shared/types/global';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { documentFileUpload, ScoringReportDataType } from '@/features/project-management/types/project';
import { useCurrentStep, useProjectInfo, useProjectName, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { useParams } from 'next/navigation';
import { EDescriptionCopilotkit, EEndpointApiCopilotkit, ETaskNameCopilot } from '@/shared/enums/global';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import QuestionnaireAnalysis from '../../discovery-questionnaire/QuestionnaireAnalysis';
import AnalysisReportQualitative from './AnalysisReportQualitative';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { markdownToHTMLToDocFile } from '@/shared/components/ui/editor/parser';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import MessageWarning from '../../common/MessageWarning';
import { useLocale, useTranslations } from 'next-intl';
import { useCopilotAction, useCopilotChat, useCopilotMessagesContext } from '@copilotkit/react-core';
import { useSaveMessageAI } from '@/features/project-management/hooks/useSaveMessageAI';
import { useMessageGetList } from '@/features/project-management/hooks/useMessageGetList';
import { useChatBoxMessage, useChatBoxSetOnSubmitCallback } from '@/features/project-management/stores/chatbox-store';
import type { MessageType } from '@/features/project-management/types/chat';
import type { TextMessage } from '@copilotkit/runtime-client-gql';
import { ConvertMessageFromConversation, handleSaveMessage } from '@/features/project-management/utils/chat';
import AIResult from '../../common/AIResult';
import { debounce } from '@/shared/utils/debounce';

type QuestionViewType = 'questionnaire' | 'analysis';

type QualityQuestionType = {
  data: any[];
  stepId: string;
  id: string;
  templates: IFileResponse[];
  evaluationFramework: string;
  nameForm: string;
  conversationData: ConversationDataType[];
  onBackDashboard: () => void;
  onOpenDetailScore: (data: string) => void;
};

export type QualitativeTypeRef = {
  onReGen: () => void;
};

const QualitativeQuestionnaireWrapper: React.FC<QualityQuestionType> = ({
  data,
  stepId,
  id,
  templates,
  evaluationFramework,
  nameForm,
  conversationData,
  onBackDashboard,
  onOpenDetailScore,
}) => {
  const t = useTranslations('workflow');

  const toggleOptions = [
    { id: 'questionnaire' as QuestionViewType, label: t('research.questionnaire') },

  ];

  const locale = useLocale();

  const summarizedOption = { id: 'analysis' as QuestionViewType, label: t('research.analysis') };

  const [options, setOptions] = useState<OptionChangeViewType[]>(toggleOptions);

  const [selectedType, setSelectedType] = useState<QuestionViewType>('questionnaire');

  const [quality, setQuality] = useState<string>('');

  const [statusData, _setStatusData] = useState<boolean>(false);

  const [statusAnalysisData, _setStatusAnalysisData] = useState<boolean>(false);

  const [isAnalysis, setIsAnalysis] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState(true);

  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);

  const [isFirstCall, setIsFirstCall] = useState<boolean>(false);

  const [scoringData, setScoringData] = useState<ScoringReportDataType | null>(null);

  const [isReGenData, setIsReGenData] = useState<boolean>(false);

  const [isReGenAnalysis, setIsReGenAnalysis] = useState<boolean>(false);

  const [analysisBrief, setAnalysisBrief] = useState<string>('');

  const [idQuality, setIdQuality] = useState<string>('');

  const [idScoring, setIdScoring] = useState<string>('');

  const [filesUpload, setFilesUpload] = useState<IFileResponse[]>([]);

  const [modelAIQuesDefault, setModelAIQuesDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAIQuesSelected, setModelAIQuesSelected] = useState<string>(EValueModelAI.GPT);

  const [modelAIAnalysisDefault, setModelAIAnalysisDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAIAnalysisSelected, setModelAIAnalysisSelected] = useState<string>(EValueModelAI.GPT);

  const [report, setReport] = useState<string>('');

  const params = useParams<{ id: string }>();

  const workflow = useWorkflowTasks();

  const idSOW = workflow[2]?.steps[1]?.id;

  const { data: dataSOW } = useGetInfoDetail<any, documentFileUpload>(idSOW ?? '');

  const abortControllerRef = useRef<AbortController | null>(null);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const currentStep = useCurrentStep();

  const projectName = useProjectName();

  const queryClient = useQueryClient();

  const ref = useRef<QualitativeTypeRef | null>(null);

  const {
    completeStep,
  } = useWorkflowActions();

  const project = useProjectInfo();

  const lastMessage = useChatBoxMessage();

  const { reset } = useCopilotChat();

  const { mutateAsync: saveMessageAI } = useSaveMessageAI();

  const lastSavedId = useRef<string | null>(null);

  const isInitialMessageRef = useRef<boolean>(true);

  const { messages, setMessages } = useCopilotMessagesContext();

  const [idConversation, setIdConversation] = useState<string>('');

  const { data: messageList } = useMessageGetList(idConversation);

  const setOnSubmitCallback = useChatBoxSetOnSubmitCallback();

  const conversationIdAnalysis = conversationData.find(c => c.order === 1)?.id ?? '';

  const conversationIdQuestionnaire = conversationData.find(c => c.order === 0)?.id ?? '';

  useEffect(() => {
    isInitialMessageRef.current = true;
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setIdConversation(selectedType === 'analysis' ? conversationIdAnalysis : conversationIdQuestionnaire);
  }, [conversationIdAnalysis, conversationIdQuestionnaire, selectedType]);

  // Function to handle when a message is submitted in ChatBox
  const handleChatBoxSubmit = useCallback(() => {
    isInitialMessageRef.current = false;
  }, []);

  // Register the callback with the chatBox store
  useEffect(() => {
    setOnSubmitCallback(handleChatBoxSubmit);

    // Cleanup: remove the callback when component unmounts
    return () => {
      setOnSubmitCallback(null);
    };
  }, [setOnSubmitCallback, handleChatBoxSubmit]);

  const saveMessage = useCallback(async (conversationId: string, data: TextMessage) => {
    await saveMessageAI({
      conversationId,
      data,
    });
  }, []);

  useEffect(() => {
    const conversationId = (selectedType === 'analysis' ? conversationIdAnalysis : conversationIdQuestionnaire);

    const CheckConversationIdExist = () => {
      if (!conversationId) {
        // eslint-disable-next-line no-useless-return
        return;
      }
    };

    const latest = handleSaveMessage(
      messages,
      lastSavedId,
      isInitialMessageRef,
      conversationId,
      saveMessage,
      CheckConversationIdExist,
    );
    latest.then(
      res => lastSavedId.current = res?.id ?? '',
    );
  }, [messages, selectedType, conversationIdAnalysis, conversationIdQuestionnaire, saveMessage]);

  useEffect(() => {
    reset();
    if (messageList && messageList.items.length) {
      const messages = [...messageList.items];
      const dataMessage = (messages.reverse().flatMap(d => d.data[0]) ?? []) as MessageType[];

      setMessages(ConvertMessageFromConversation(dataMessage));
    }

    return () => {
      reset();
    };
  }, [messageList, setMessages, reset]);

  const handleSelectType = (option: OptionChangeViewType) => {
    if (isLoading) {
      return;
    }
    setSelectedType(option.id as QuestionViewType);
  };

  useEffect(() => {
    if (dataSOW && dataSOW.stepInfo.length) {
      const brief = dataSOW?.stepInfo[0]?.infos[0]?.value ?? '';

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setAnalysisBrief(brief);
    }
  }, [dataSOW]);

  const saveDataFromAI = async (markdown: string, status: EStatusTask) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 6,
          type: EQuantitative.QUESTIONNAIRE,
          infos: [
            { value: markdown, status },
          ],
          model: modelAIQuesSelected,
        },

      ],
    };

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const getDataQuality = async () => {
    if (isFirstCall) {
      return;
    }

    if ((!analysisBrief)) {
      return;
    }

    setIsFirstCall(true);
    const payload = {
      project_id: params.id,
      brief_analysis: analysisBrief,
      llm: modelAIQuesSelected,
      questionnaire_template_url: [{
        key: 'https://minastik-store.s3.ap-northeast-1.amazonaws.com/templates/3.+Questionnaire_BrandBeatScore-Quali+.docx',
      }],
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.QUALITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const quality = res.data.result;

      setIsLoading(false);
      if (quality) {
        setQuality(quality);
      }

      if (!quality) {
        return;
      }
      saveDataFromAI(quality, EStatusTask.IN_PROGRESS);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (!data.length) {
      getDataQuality();
    } else {
      const quality = data.find(d => d.type === EQuantitative.QUESTIONNAIRE);
      if (quality) {
        const markdown = quality.infos[0].value;
        const status = quality.infos[0].status;

        const model = quality.model;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIQuesDefault(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIQuesSelected(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setQuality(markdown);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(false);

        // setStatusData(status === EStatusTask.COMPLETED);
        if (status === EStatusTask.COMPLETED) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
          setOptions([...toggleOptions, summarizedOption]);
        }
      } else {
        getDataQuality();
      }

      const report = data.find(d => d.type === EQuantitative.ANALYSIS);
      const files = data.find(d => d.type === EQuantitative.FILES);
      if (report) {
        const markdown = report.infos[0].form;
        // const status = report.infos[0]?.status;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdQuality(report.id);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setReport(markdown);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsAnalysis(true);

        // if (status) {
        // // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        //   setStatusAnalysisData(status === EStatusTask.COMPLETED);
        // } else {
        // // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        //   setStatusAnalysisData(false);
        // }
      }

      if (files) {
        const filesUpload = files.infos[0].files;

        const model = files.model;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setOptions([...toggleOptions, summarizedOption]);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIAnalysisDefault(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIAnalysisSelected(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setFilesUpload(filesUpload);
      }

      const scoringData = data.find(d => d.type === EQuantitative.SCORING);

      if (scoringData) {
        const data = scoringData.infos[0].value;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdScoring(scoringData.id);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setScoringData(data);
      }

      const isGenInform = data.some(d => d.isGenerate && d.order === 6);
      const isGen = data.some(d => d.isGenerate && d.order !== 6 && d.order !== 0);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsReGenData(isGenInform);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsReGenAnalysis(isGen);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, analysisBrief]);

  const isChangedModel = useMemo(() => {
    return modelAIQuesDefault !== modelAIQuesSelected;
  }, [modelAIQuesDefault, modelAIQuesSelected]);

  const isChangedModelAnalysis = useMemo(() => {
    return modelAIAnalysisDefault !== modelAIAnalysisSelected;
  }, [modelAIAnalysisDefault, modelAIAnalysisSelected]);

  const onSubmit = () => {};

  const onComplete = debounce(() => {
    completeStep(stepId);
  }, 300);

  const onCompleteMarkdown = async (markdown: string) => {
    const id: string[] = data.filter(d => d.order !== 6 && d.order !== 0).map(t => t.id);
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: [],
      select: 'all',
      isGenerate: true,
      stepInfoIdsGenerate: (selectedType === 'questionnaire') ? id : [],
    });

    await saveDataFromAI(markdown, EStatusTask.COMPLETED);

    toast.success(t('deskResearch.questionnaireSaved'));
  };

  const handleEnhanceQuestionnaireForm = async () => {
    const projectId = project?.id ?? '';

    const data = {
      project_id: projectId,
      llm: modelAIQuesSelected,
      task_name: ETaskNameCopilot.QUALITY_QUESTIONNAIRE,
      instructions: lastMessage,
      original_content: quality,
    };

    const baseUrl = window.location.origin;
    const abortController = new AbortController();
    abortControllerRef.current = abortController;

    const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
      method: 'POST',
      body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.EDIT_CONTENT }),
      signal: abortControllerRef.current.signal,
    });

    const res = await response.json();

    const { result } = res.data;

    const { explain, output } = result;

    setQuality((output));

    saveDataFromAI(output, EStatusTask.COMPLETED);

    return explain;
  };

  useCopilotAction({
    name: 'updateQualityQuestionnaire',
    description: EDescriptionCopilotkit.EDIT_QUALITY_QUESTIONNAIRE,
    available: (selectedType === 'questionnaire') ? 'enabled' : 'disabled',
    parameters: [],
    handler: async () => {
      return await handleEnhanceQuestionnaireForm();
    },
    render: ({ result }) => {
      return <AIResult result={result} />;
    },
  });

  const handleReGen = async () => {
    if (selectedType === 'analysis') {
      setIsLoadingAnalysis(true);
      setScoringData(null);
      setIdQuality('');
      setReport('');
      const id: string[] = data.filter(d => d.order !== 6 && d.order !== 0).map(t => t.id);
      await refreshData({
        id: stepId,
        status: EStatusTask.COMPLETED,
        stepIds: [],
        stepInfoIds: id,
        select: 'all',
        isGenerate: true,
        stepInfoIdsGenerate: [],
      });

      if (ref.current) {
        setIsReGenAnalysis(false);
        ref.current.onReGen();
      }
      return;
    }
    const idsInForm = data.filter(d => d.order === 6).map(d => d.id);
    const idChangeReGen = data.filter(d => d.order !== 6).map(d => d.id);
    setIsLoading(true);
    setIsReGenData(false);
    setQuality('');
    setScoringData(null);
    // setStatusAnalysisData(false);
    setIsFirstCall(false);
    setIdQuality('');
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: idsInForm,
      stepInfoIdsGenerate: idChangeReGen,
      select: 'all',
      isGenerate: true,
    });

    queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(selectedType === 'questionnaire' ? quality : report);

    await downloadMDToFile(html, projectName, nameStep[locale]!, nameForm, selectedType);
  };

  const handleChangeModel = (model: string) => {
    if (selectedType === 'questionnaire') {
      setModelAIQuesSelected(model);
    } else if (selectedType === 'analysis') {
      setModelAIAnalysisSelected(model);
    }
  };

  return (
    <>
      <div className="sticky top-[70px] bg-white z-100 pb-2 dark:bg-black dark:text-white">
        <HeaderResearch
          textCopy={quality}
          // isViewCopyBox={selectedType === 'questionnaire' && !!quality && statusData}
          isViewCopyBox={false}
          header={t('deskResearch.discoveryHeader')}
          description={t('deskResearch.descriptionHeader')}
          options={options}
          selectedType={selectedType}
          isHiddenBackButton={selectedType === 'questionnaire' || (selectedType === 'analysis' && !isAnalysis)}
          handleSelectedType={handleSelectType}
          onClickApprove={onSubmit}
          onSaveAndNextStep={onComplete}
          onBackDashboard={onBackDashboard}
          onBackUploadFile={() => setIsAnalysis(false)}
          onOpenDetailScore={onOpenDetailScore}
          isScoringAI={selectedType === 'analysis' && !!scoringData}
          dataScoring={scoringData}
          isShowButtonReGen={
            selectedType === 'questionnaire'
              ? (isReGenData || isChangedModel)
              : ((isReGenAnalysis || isChangedModelAnalysis) && isAnalysis)
          }
          onReGen={handleReGen}
          isShowDownloadIcon={selectedType === 'questionnaire' ? !!quality : !!report}
          onDownloadFile={handleDownloadFile}
          onChangeModel={handleChangeModel}
          modelAIDefault={
            selectedType === 'questionnaire'
              ? modelAIQuesDefault
              : modelAIAnalysisDefault
          }
          isShowModel={(
            selectedType === 'questionnaire' && !isLoading)
          || (selectedType === 'analysis' && !isLoadingAnalysis)}
        />
      </div>

      {isLoading
        ? (
            <div className="mt-4">
              <ProjectCardSkeleton />
              <MessageWarning />
            </div>
          )
        : selectedType === 'questionnaire'
          ? (
              <div className="mt-4">
                <QuestionnaireAnalysis
                  stepId={stepId}
                  data={quality}
                  isHiddenBackButton={true}
                  isHiddenApproveButton={statusData}
                  isFinish={statusData}
                  onBack={() => {}}
                  onSubmit={onCompleteMarkdown}
                  onConfirm={onCompleteMarkdown}
                />
              </div>
            )
          : (
              <div className="mt-4">
                <AnalysisReportQualitative
                  ref={ref}
                  summary={quality}
                  templates={templates}
                  report={report}
                  id={id}
                  stepId={stepId}
                  idQuality={idQuality}
                  idScoring={idScoring}
                  isAnalysis={isAnalysis}
                  evaluationFramework={evaluationFramework}
                  conversationIdAnalysis={conversationIdAnalysis}
                  isFinish={statusAnalysisData}
                  fileUpload={filesUpload}
                  setIsAnalysis={setIsAnalysis}
                  setScoringData={setScoringData}
                  isLoading={isLoadingAnalysis}
                  setIsLoading={setIsLoadingAnalysis}
                  modelSelected={modelAIAnalysisSelected}
                  modelDefault={modelAIAnalysisDefault}
                  selectedType={selectedType}
                />
              </div>
            )}
    </>
  );
};

export default QualitativeQuestionnaireWrapper;
