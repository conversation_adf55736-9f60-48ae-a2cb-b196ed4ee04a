'use client';

import type { infosQA, StepInfosQA,
  // StepInfosQA
} from '@/features/project-management/types/step';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import TextArea from '@/shared/components/form/input/TextArea';
import Label from '@/shared/components/form/Label';
import { useCoAgent } from '@copilotkit/react-core';
import { zodResolver } from '@hookform/resolvers/zod';
import React, {
  useEffect,

  // useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import FileUpload from './FileUpload';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import {
  useCurrentStep,
  useCurrentTask,
  useEvaluationActions,
  // useCurrentStep,
  useWorkflowActions,
} from '@/features/project-management/stores/project-workflow-store';
import { createDynamicSchema, createPromptData, generateDefaultValues, hasDataChanged,
  // hasDataChanged
} from '@/features/project-management/utils/initialScreeningUtils';
import type { DynamicFormData } from '@/features/project-management/utils/initialScreeningUtils';
import { compareObject, compareObjectArray } from '@/shared/utils/compareObject';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import type { documentUrlCrew, questionResponseCrew } from '@/features/project-management/types/project';
import type { summarizeFlow } from '@/features/project-management/types/agent';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { Button } from '@/shared/components/ui/button';
import type { WorkflowTypeRef } from '../layout/WorkflowWrapper';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { useQueryClient } from '@tanstack/react-query';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import SelectModelAI from '../common/SelectModelAI';
import { useTranslations } from 'next-intl';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';
import { debounce } from '@/shared/utils/debounce';

type stateDataPayload = {
  payload: StepInfosPayload | null;
  formData: any | null;
  infos: questionResponseCrew[];
  fileResponse: documentUrlCrew[];
};

type InitialScreeningFormType = {
  ref?: React.Ref<WorkflowTypeRef>;
};

const InitialScreeningForm: React.FC<InitialScreeningFormType> = () => {
  const t = useTranslations('workflow');

  // Sentry.captureException(new Error(' Lỗi aaaa'));
  // State management
  const [files, setFiles] = useState<IFileResponse[]>([]);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [isEditing, setIsEditing] = useState(true);

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [statePayload, setStatePayload] = useState<stateDataPayload>({
    payload: null,
    formData: null,
    infos: [],
    fileResponse: [],
  });

  const titleConfirm = t('common.titleConfirmChange');

  const titleUnSave = t('common.titleUnSave');

  const descriptionUnSave = t('common.descriptionUnSave');

  const descriptionConfirm = t('common.descriptionConfirm');

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);
  // Refs for preventing unnecessary re-renders and data processing
  const formResetTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const lastProcessedDataRef = useRef<string>('');

  const [currentDataStep, setCurrentDataStep] = useState<DynamicFormData>({});

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const isVisible = useChatBoxVisible();

  // Store hooks
  const currentStep = useCurrentStep();
  const currentTask = useCurrentTask();

  // Memoize stable references to prevent unnecessary re-renders
  const currentStepId = currentStep?.id;
  const currentStepData = currentStep?.data;
  const currentStepQAList = currentStep?.qaList;
  // const currentStepStatus = currentStep?.status;

  // const workflow = useWorkflowTasks();

  // const params = useParams<{ id: string }>();

  // Store action functions - using individual hooks for stable references
  const {
    updateStepData,
    completeStep,
    updateQAListData,
    // setProjectName,
    updateStatus,
    updateCurrentStepInfoIds,
    getNextStepId,
  } = useWorkflowActions();

  const { updateScoreDetail } = useEvaluationActions();

  // const show = useChatBoxShow();

  const { mutateAsync } = useUpdateStatusStep();

  const { registerStep, clearStep } = useDirty();

  // API hooks
  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { data: qaStep, isLoading: isQALoading, error: _qaError } = useGetInfoDetail<infosQA, null>(currentStep?.id ?? '');

  const queryClient = useQueryClient();

  // const { data: project } = useProjectDetail(params.id);
  // AI agent hooks
  // const { appendMessage } = useCopilotChat();
  const { state: _coAgentState, setState: setCoAgentsState } = useCoAgent<stateRouteAgent<summarizeFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
      agent_name: AGENT_NAME_COPILOTKIT.SUMMARIZE,
    },
  });

  // Compute loading state                   based on React Query state
  const isDataLoading = isQALoading || (!qaStep && !!currentStep?.id);

  // Generate dynamic schema based on current step qaList
  const dynamicSchema = useMemo(() => {
    if (!currentStepQAList) {
      return z.object({});
    }
    return createDynamicSchema(currentStepQAList);
  }, [currentStepQAList]);

  // Generate default values based on current step data
  const formDefaultValues = useMemo(() => {
    if (!currentStepQAList) {
      return {};
    }
    return generateDefaultValues(currentStepQAList, currentStepData);
  }, [currentStepId]);

  // Initialize React Hook Form with dynamic schema
  const {
    control,
    handleSubmit,
    reset,
    getValues,
    formState: { errors, isSubmitting, isDirty },
  } = useForm<DynamicFormData>({
    resolver: zodResolver(dynamicSchema),
    defaultValues: formDefaultValues,
    mode: 'onSubmit',
  });

  useEffect(() => {
    if (!currentStepId) {
      return;
    }
    const isChangedFiled = !compareObjectArray(initialFile, files);

    const isChangedModel = modelAIDefault !== modelAISelected;

    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    _setIsSaved(!((isDirty && isEditing) || isChangedFiled || isChangedModel));

    registerStep(currentStepId, () => ((isDirty || isChangedFiled || isChangedModel) && isEditing));
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDirty, files, currentStepId, initialFile, modelAISelected, modelAIDefault]);

  // useEffect(() => {
  // // FIXME: update later when using Copilotkit
  //   // setCoAgentsState({
  //   //   ...coAgentState,
  //   //   [ENameStateAgentCopilotkit.SUMMARIZE]: {
  //   //     ...(project ? { project_info: { ...project } } : {}),
  //   //   },
  //   // });

  // }, [project]);

  // Memoize the processed QA data to prevent unnecessary recalculations
  const processedQAData = useMemo(() => {
    if (!qaStep) {
      return null;
    }

    const infos = qaStep.stepInfo[qaStep.stepInfo.length - 1]?.infos ?? [];
    const stepInfo = infos.slice(0, -1);
    const filesResponse = (infos[infos.length - 1]?.url ?? []) as IFileResponse[];
    const modelSelected = (infos[infos.length - 1]?.modelAI ?? EValueModelAI.GPT);
    const qaList: StepInfosQA[] = stepInfo ?? [];
    const newQaList = qaList.map(qa => ({
      ...qa,
      name: qa.question.replace(/[^a-z0-9\s]/gi, '').replace(/\s+/g, '_').toLowerCase(),
    }));

    const data = newQaList.reduce((acc, i) => {
      acc[i.name] = i.answer as string;
      return acc;
    }, {} as Record<string, string>);

    setCurrentDataStep(data);
    if (currentStep?.status === EStatusTask.COMPLETED) {
      setIsEditing(false);
    }
    setInitialFile(filesResponse);
    setFiles(filesResponse);
    setModelAIDefault(modelSelected);
    setModelAISelected(modelSelected);
    return {
      qaList: newQaList,
      data,
    };
  }, [qaStep]);

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  // Consolidated Data Processing & Store Updates
  useEffect(() => {
    if (!processedQAData || !currentStepId) {
      return;
    }

    const { qaList, data } = processedQAData;

    // Create a unique key for this data to prevent duplicate processing
    const dataKey = `${currentStepId}-${JSON.stringify(data)}-${qaList.length}`;
    if (lastProcessedDataRef.current === dataKey) {
      return;
    }

    // Check if the data has actually changed to prevent infinite loops
    const hasStepDataChanged = hasDataChanged(currentStepData, data);
    const hasQAListChanged = hasDataChanged(currentStepQAList, qaList);

    // Only update if the data has actually changed
    if (hasStepDataChanged || hasQAListChanged) {
      // Update store data
      updateStepData(currentStepId, data);
      updateQAListData(currentStepId, qaList);

      // Update step info IDs if available
      if (qaStep?.stepInfo?.[0]?.id) {
        updateCurrentStepInfoIds(qaStep.stepInfo[0].id);
      }

      clearStep(currentStepId);

      // Mark this data as processed
      lastProcessedDataRef.current = dataKey;
    }
  }, [processedQAData, currentStepId, currentStepData, currentStepQAList, qaStep]);

  // Consolidated Form Management
  useEffect(() => {
    if (!processedQAData && !currentStepData) {
      return;
    }

    // Clear any existing timeout to prevent multiple resets
    if (formResetTimeoutRef.current) {
      clearTimeout(formResetTimeoutRef.current);
    }

    // Debounce form reset to prevent excessive calls
    formResetTimeoutRef.current = setTimeout(() => {
      const newDefaultValues: DynamicFormData = {};

      // Use processedQAData if available, otherwise use currentStepQAList
      const qaListToUse = processedQAData?.qaList || currentStepQAList || [];

      qaListToUse.forEach((qa) => {
        newDefaultValues[qa.name] = currentStepData?.[qa.name] || '';
      });

      reset(newDefaultValues);

      // Handle files separately if they exist in currentStepData
      if (currentStepData?.files && Array.isArray(currentStepData.files)) {
        setFiles(currentStepData.files);
      }
    }, 50); // Small delay to batch updates

    return () => {
      if (formResetTimeoutRef.current) {
        clearTimeout(formResetTimeoutRef.current);
      }
    };
  }, [processedQAData, currentStepData, currentStepQAList, reset]);

  // Memoized event handlers to prevent unnecessary re-renders
  const handleCreatePrompt = React.useCallback((data: DynamicFormData, files: IFileResponse[]) => {
    return createPromptData(data, files);
  }, []);

  const handleSendPrompt = React.useCallback((questions: questionResponseCrew[], document_url: documentUrlCrew[]) => {
    setCoAgentsState((prevState: any) => ({
      ...prevState,
      [ENameStateAgentCopilotkit.SUMMARIZE]: {
        ...prevState[ENameStateAgentCopilotkit.SUMMARIZE],
        initial_info: [...questions],
        document_url: [...document_url],
      },
    }));

  // FIXME: update later when using Copilotkit
    // appendMessage(
    //   new TextMessage({
    //     content: MESSAGE_SEND_ROUTE_AGENT,
    //     role: Role.Developer,
    //   }),
    // );
  }, [setCoAgentsState]); // [appendMessage] | Removed coAgentState to prevent re-renders

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  // FIXME: update later when using Copilotkit
  const toggleViewMode = () => {
    const data = getValues();
    const isChanged = !compareObject(data, currentDataStep);
    const isChangedFiled = !compareObjectArray(files, initialFile);
    const isChangeModel = modelAIDefault !== modelAISelected;

    if (isEditing && (isChanged || isChangedFiled || isChangeModel)) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
    }

    if (isClickUnSaved) {
      setIsClickUnSaved(false);
    }
    setIsEditing(prev => !prev);
  };

  const updateQuestion = (data: StepInfosPayload, id: string) => {
    return updateQuestionAnswer(data, id);
  };

  const callCompleteStep = debounce(() => {
    completeStep(currentStepId!);
  }, 300);

  const handleNextStep = React.useCallback(async (
    infos: questionResponseCrew[],
    fileResponse: documentUrlCrew[],
    formData: any,
    isChanged: boolean,
    isChangedFiled: boolean,
    isChangeModel: boolean,
  ) => {
    if (!currentStepId) {
      return;
    }
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStepId ?? '', status: EStatusTask.COMPLETED });
    }

    if (currentStep.status !== EStatusTask.COMPLETED
      || ((isChanged || isChangedFiled || isChangeModel)
        && currentStep.status === EStatusTask.COMPLETED)) {
      handleSendPrompt(infos, fileResponse);
      // show();

      // Reset data in DB
      // const id = getNextStepId();
      // Reset scoring data in next step
      updateScoreDetail({ stepInfo: [] } as any);

      // await updateQuestionAnswer({ stepInfos: [] }, id ?? '');

      if (currentTask?.status !== EStatusTask.COMPLETED) {
        mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
      }

      toast.success(t('common.noticeInitialForm'), {
        duration: 3000,
      });
    }
    updateStepData(currentStepId, formData);
    _setIsSaved(false);
    callCompleteStep();
  }, [handleSendPrompt, completeStep]);

  // Memoized form submission handler with stable dependencies
  const onSubmit = React.useCallback(async (data: DynamicFormData) => {
    if (!currentStepId) {
      return;
    }
    const isChanged = !compareObject(data, currentDataStep);

    const isChangedFiled = !compareObjectArray(initialFile, files);

    const isChangedModel = modelAIDefault !== modelAISelected;

    // Combine form data with files
    const formData = {
      ...data,
      files,
    };
    const prompt = handleCreatePrompt(data, files);
    const { questions, files: fileResponse } = prompt;
    const setQuestion: Map<string, string> = new Map();

    currentStepQAList?.forEach((qa) => {
      setQuestion.set(qa.name, qa.question);
    });

    const infos = questions.map((q, index) => ({
      question: setQuestion.get((q.questions) ?? '') ?? '',
      answer: q.answer ?? '',
      id: `${index + 1}`,
    }));

    const payload = {
      stepInfos: [{
        order: 0,
        model: EValueModelAI.GPT,
        infos: [...infos, {
          url: fileResponse.map(file => ({
            ...file,
            key: file.key,
            originalname: file.originalname,
            id: file.id,
          })),

          modelAI: modelAISelected,
        }],
      }],
      // order: 0,
      // infos: dataInfos.infos,
    };

    if (infos.some(quiz => !quiz.question.length)) {
      console.log('some of the question is empty due to unexpected error');
      return;
    }
    setStatePayload(prev => ({ ...prev, payload, infos, formData }));
    if (isEditing && (isChanged || isChangedFiled || isChangedModel) && currentStep.status === EStatusTask.COMPLETED) {
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }

    if (currentStep.status !== EStatusTask.COMPLETED) {
      await updateQuestion(payload, currentStepId);
    }
    clearStep(currentStep?.id ?? '');

    handleNextStep(infos, fileResponse, formData, isChanged, isChangedFiled, isChangedModel);
  }, [currentStepId, files, handleCreatePrompt, currentStepQAList, handleNextStep, isEditing, modelAISelected]);

  // Cleanup function for component unmount
  React.useEffect(() => {
    return () => {
      if (formResetTimeoutRef.current) {
        clearTimeout(formResetTimeoutRef.current);
      }
    };
  }, []);

  const handleConfirmPopUp = async () => {
    if (isClickUnSaved) {
      reset();
      setFiles(initialFile);
      setModelAISelected(modelAIDefault);
      setIsEditing(false);
      setIsShowModal(false);
      return;
    }
    clearStep(currentStep?.id ?? '');
    const { formData, infos, payload, fileResponse } = statePayload;
    if (formData && infos && payload && currentStepId) {
      await updateQuestion(payload, currentStepId);

      await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
      updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);

      const idsInTask = currentTask?.children.filter(child => child.id !== currentStepId).map(t => t.id);

      const select = 'all';
      await mutateAsync({
        id: currentStepId,
        status: EStatusTask.PENDING,
        stepIds: idsInTask,
        select,
        stepInfoIds: [],
      });
      currentTask?.children.forEach((child) => {
        if (child.id !== currentStepId) {
          updateStatus(child.id, EStatusTask.PENDING);
        }
      });
      await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', getNextStepId()], type: 'all' });
      await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', currentStepId], type: 'all' });

      handleNextStep(infos, fileResponse, formData, true, true, true);

      setIsShowModal(false);
    }
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleChangeModelAI = (value: string) => {
    setModelAISelected(value);
  };

  // Show loading if we don't have currentStep yet
  if (!currentStep) {
    return (
      <div className="relative">
        <div className="space-y-6 p-4 md:p-6">
          <ProjectCardSkeleton />
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <form onSubmit={handleSubmit(onSubmit)} className="relative space-y-6 p-4 md:p-6">
        {/* Dynamically render form fields based on qaList */}
        {isDataLoading
          ? (
              <ProjectCardSkeleton />
            )
          : (

              <React.Fragment>
                {!isVisible && (
                  <SelectModelAI
                    onChangeModel={handleChangeModelAI}
                    defaultValue={modelAIDefault}
                    disable={!isEditing}
                  />
                )}

                {currentStepQAList?.map(qa => (
                  <div key={qa.id}>
                    <Label htmlFor={qa.name} className="mb-1.5 block text-primary">
                      {qa.question}
                    </Label>
                    <Controller
                      name={qa.name}
                      control={control}
                      render={({ field }) => (
                        <TextArea
                          placeholder={t('common.placeholderAnswer')}
                          rows={3}
                          value={field.value}
                          onChange={field.onChange}
                          error={!!errors[qa.name]}
                          hint={errors[qa.name]?.message}
                          disabled={!isEditing}
                        />
                      )}
                    />
                  </div>
                ))}
              </React.Fragment>
            )}

        {/* File Upload */}
        <div>
          <Label htmlFor="files" className="mb-1.5 block text-primary">
            {t('common.attachFile')}
          </Label>
          <FileUpload isDisable={!isEditing} onFilesChange={handleFilesChange} initialFile={initialFile} />
        </div>
      </form>

      <WorkflowNavigation
        onComplete={handleSubmit(onSubmit)}
        disableNext={isSubmitting}
        showPrevious={false}
      >
        { currentStep && currentStep.status === EStatusTask.COMPLETED && (
          <Button
            type="button"
            className={`bg-warning-400 text-white rounded-lg hover:bg-warning-500 ${isEditing ? 'bg-error-500 hover:bg-error-600' : ''}`}
            onClick={toggleViewMode}
            disabled={isSubmitting}
          >
            {isEditing ? t('common.cancel') : t('common.edit')}
          </Button>
        )}
      </WorkflowNavigation>

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titlePopup}
        description={descriptionPopUp}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </div>
  );
};

export default InitialScreeningForm;
